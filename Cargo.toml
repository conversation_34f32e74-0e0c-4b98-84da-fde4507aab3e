[package]
name = "learn-wgpu"
version = "0.1.0"
edition = "2024"

[dependencies]
# Make sure to include your existing wgpu dependency, for example:
# wgpu = "0.13" # Or your specific version

# Add or modify the winapi dependency to include the "winbase" feature.
# The log indicates winapi version 0.3.9 is involved.
winapi = { version = "0.3.9", features = ["winbase"] }

tracing = "0.1.36"
tracing-subscriber = "0.3.15"
wgpu = "0.13.1"
winit = "0.27.2"